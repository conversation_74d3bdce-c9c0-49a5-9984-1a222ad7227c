services:
  # Database
  postgres:
    image: postgres:17
    container_name: atma-postgres
    environment:
      POSTGRES_DB: atma_db
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
      POSTGRES_INITDB_ARGS: "--encoding=UTF8"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-db.sql:/docker-entrypoint-initdb.d/00-init-user.sql
      - ./current_database_dump.sql:/docker-entrypoint-initdb.d/01-init.sql
    ports:
      - "5432:5432"
    networks:
      - atma-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d atma_db"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: atma-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - atma-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # RabbitMQ Message Broker
  rabbitmq:
    image: rabbitmq:3-management-alpine
    container_name: atma-rabbitmq
    environment:
      RABBITMQ_DEFAULT_USER: guest
      RABBITMQ_DEFAULT_PASS: guest
    ports:
      - "5672:5672"
      - "15672:15672"  # Management UI
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq
    networks:
      - atma-network
    healthcheck:
      test: ["CMD", "rabbitmq-diagnostics", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Auth Service
  auth-service:
    build:
      context: ./auth-service
      dockerfile: Dockerfile
    container_name: atma-auth-service
    environment:
      - NODE_ENV=production
      - PORT=3001
      - DB_HOST=postgres
      - DB_PORT=5432
      - DB_NAME=atma_db
      - DB_USER=postgres
      - DB_PASSWORD=password
      - DB_DIALECT=postgres
      - DB_SCHEMA=auth
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - JWT_SECRET=atma_secure_jwt_secret_key_f8a5b3c7d9e1f2a3b5c7d9e1f2a3b5c7
      - INTERNAL_SERVICE_KEY=internal_service_secret_key_change_in_production
      - DEFAULT_TOKEN_BALANCE=5
      - BCRYPT_ROUNDS=10
      - LOG_LEVEL=info
    ports:
      - "3001:3001"
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - atma-network
    volumes:
      - ./auth-service/logs:/app/logs
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "node", "-e", "require('http').get('http://localhost:3001/health', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) }).on('error', () => process.exit(1))"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Archive Service
  archive-service:
    build:
      context: ./archive-service
      dockerfile: Dockerfile
    container_name: atma-archive-service
    environment:
      - NODE_ENV=production
      - PORT=3002
      - DB_HOST=postgres
      - DB_PORT=5432
      - DB_NAME=atma_db
      - DB_USER=postgres
      - DB_PASSWORD=password
      - DB_DIALECT=postgres
      - DB_SCHEMA=archive
      - JWT_SECRET=atma_secure_jwt_secret_key_f8a5b3c7d9e1f2a3b5c7d9e1f2a3b5c7
      - AUTH_SERVICE_URL=http://auth-service:3001
      - INTERNAL_SERVICE_KEY=internal_service_secret_key_change_in_production
      - LOG_LEVEL=info
    ports:
      - "3002:3002"
    depends_on:
      postgres:
        condition: service_healthy
      auth-service:
        condition: service_started
    networks:
      - atma-network
    volumes:
      - ./archive-service/logs:/app/logs
    restart: unless-stopped

  # Assessment Service
  assessment-service:
    build:
      context: ./assessment-service
      dockerfile: Dockerfile
    container_name: atma-assessment-service
    environment:
      - NODE_ENV=production
      - PORT=3003
      - DB_HOST=postgres
      - DB_PORT=5432
      - DB_NAME=atma_db
      - DB_USER=postgres
      - DB_PASSWORD=password
      - DB_DIALECT=postgres
      - DB_SCHEMA=assessment
      - RABBITMQ_URL=amqp://guest:guest@rabbitmq:5672
      - JWT_SECRET=atma_secure_jwt_secret_key_f8a5b3c7d9e1f2a3b5c7d9e1f2a3b5c7
      - AUTH_SERVICE_URL=http://auth-service:3001
      - ARCHIVE_SERVICE_URL=http://archive-service:3002/archive
      - INTERNAL_SERVICE_KEY=internal_service_secret_key_change_in_production
      - QUEUE_NAME=assessment_analysis
      - EXCHANGE_NAME=atma_exchange
      - ROUTING_KEY=analysis.process
      - QUEUE_DURABLE=true
      - MESSAGE_PERSISTENT=true
      - EVENTS_EXCHANGE_NAME=atma_events_exchange
      - EVENTS_QUEUE_NAME_ASSESSMENTS=analysis_events_assessments
      - LOG_LEVEL=info
    ports:
      - "3003:3003"
    depends_on:
      postgres:
        condition: service_healthy
      rabbitmq:
        condition: service_healthy
      auth-service:
        condition: service_healthy
      archive-service:
        condition: service_started
    networks:
      - atma-network
    volumes:
      - ./assessment-service/logs:/app/logs
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "node", "-e", "require('http').get('http://localhost:3003/health', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) }).on('error', () => process.exit(1))"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Notification Service
  notification-service:
    build:
      context: ./notification-service
      dockerfile: Dockerfile
    container_name: atma-notification-service
    environment:
      - NODE_ENV=production
      - PORT=3005
      - JWT_SECRET=atma_secure_jwt_secret_key_f8a5b3c7d9e1f2a3b5c7d9e1f2a3b5c7
      - INTERNAL_SERVICE_KEY=internal_service_secret_key_change_in_production
      - RABBITMQ_URL=amqp://guest:guest@rabbitmq:5672
      - EVENTS_EXCHANGE_NAME=atma_events_exchange
      - EVENTS_QUEUE_NAME_NOTIFICATIONS=analysis_events_notifications
      - LOG_LEVEL=info
    ports:
      - "3005:3005"
    depends_on:
      rabbitmq:
        condition: service_healthy
    networks:
      - atma-network
    volumes:
      - ./notification-service/logs:/app/logs
    restart: unless-stopped

  # Analysis Worker (3 instances)
  analysis-worker-1:
    build:
      context: ./analysis-worker
      dockerfile: Dockerfile
    container_name: atma-analysis-worker-1
    environment:
      - NODE_ENV=production
      - RABBITMQ_URL=amqp://guest:guest@rabbitmq:5672
      - QUEUE_NAME=assessment_analysis
      - EXCHANGE_NAME=atma_exchange
      - ROUTING_KEY=analysis.process
      - WORKER_INSTANCE_ID=worker-1
      - WORKER_CONCURRENCY=5
      - ARCHIVE_SERVICE_URL=http://archive-service:3002/archive
      - NOTIFICATION_SERVICE_URL=http://notification-service:3005
      - ASSESSMENT_SERVICE_URL=http://assessment-service:3003
      - INTERNAL_SERVICE_KEY=internal_service_secret_key_change_in_production
      - LOG_LEVEL=info
      - LOG_FILE=logs/analysis-worker-1.log
    depends_on:
      rabbitmq:
        condition: service_healthy
      archive-service:
        condition: service_started
      assessment-service:
        condition: service_started
    networks:
      - atma-network
    volumes:
      - ./analysis-worker/logs:/app/logs
    restart: unless-stopped

  analysis-worker-2:
    build:
      context: ./analysis-worker
      dockerfile: Dockerfile
    container_name: atma-analysis-worker-2
    environment:
      - NODE_ENV=production
      - RABBITMQ_URL=amqp://guest:guest@rabbitmq:5672
      - QUEUE_NAME=assessment_analysis
      - EXCHANGE_NAME=atma_exchange
      - ROUTING_KEY=analysis.process
      - WORKER_INSTANCE_ID=worker-2
      - WORKER_CONCURRENCY=5
      - ARCHIVE_SERVICE_URL=http://archive-service:3002/archive
      - NOTIFICATION_SERVICE_URL=http://notification-service:3005
      - ASSESSMENT_SERVICE_URL=http://assessment-service:3003
      - INTERNAL_SERVICE_KEY=internal_service_secret_key_change_in_production
      - LOG_LEVEL=info
      - LOG_FILE=logs/analysis-worker-2.log
    depends_on:
      rabbitmq:
        condition: service_healthy
      archive-service:
        condition: service_started
      assessment-service:
        condition: service_started
    networks:
      - atma-network
    volumes:
      - ./analysis-worker/logs:/app/logs
    restart: unless-stopped

  analysis-worker-3:
    build:
      context: ./analysis-worker
      dockerfile: Dockerfile
    container_name: atma-analysis-worker-3
    environment:
      - NODE_ENV=production
      - RABBITMQ_URL=amqp://guest:guest@rabbitmq:5672
      - QUEUE_NAME=assessment_analysis
      - EXCHANGE_NAME=atma_exchange
      - ROUTING_KEY=analysis.process
      - WORKER_INSTANCE_ID=worker-3
      - WORKER_CONCURRENCY=5
      - ARCHIVE_SERVICE_URL=http://archive-service:3002/archive
      - NOTIFICATION_SERVICE_URL=http://notification-service:3005
      - ASSESSMENT_SERVICE_URL=http://assessment-service:3003
      - INTERNAL_SERVICE_KEY=internal_service_secret_key_change_in_production
      - LOG_LEVEL=info
      - LOG_FILE=logs/analysis-worker-3.log
    depends_on:
      rabbitmq:
        condition: service_healthy
      archive-service:
        condition: service_started
      assessment-service:
        condition: service_started
    networks:
      - atma-network
    volumes:
      - ./analysis-worker/logs:/app/logs
    restart: unless-stopped

  # API Gateway (Main Entry Point)
  api-gateway:
    build:
      context: ./api-gateway
      dockerfile: Dockerfile
    container_name: atma-api-gateway
    environment:
      - NODE_ENV=production
      - PORT=3000
      - AUTH_SERVICE_URL=http://auth-service:3001
      - ARCHIVE_SERVICE_URL=http://archive-service:3002
      - ASSESSMENT_SERVICE_URL=http://assessment-service:3003
      - NOTIFICATION_SERVICE_URL=http://notification-service:3005
      - JWT_SECRET=atma_secure_jwt_secret_key_f8a5b3c7d9e1f2a3b5c7d9e1f2a3b5c7
      - INTERNAL_SERVICE_KEY=internal_service_secret_key_change_in_production
      - RATE_LIMIT_WINDOW_MS=900000
      - RATE_LIMIT_MAX_REQUESTS=5000
      - ALLOWED_ORIGINS=*
      - LOG_LEVEL=info
    ports:
      - "3000:3000"
    depends_on:
      - auth-service
      - archive-service
      - assessment-service
      - notification-service
    networks:
      - atma-network
    volumes:
      - ./api-gateway/logs:/app/logs
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:
  rabbitmq_data:

networks:
  atma-network:
    driver: bridge
    enable_ipv6: false
    ipam:
      driver: default
      config:
        - subnet: **********/16
